using Microsoft.VisualStudio.TestTools.UnitTesting;
using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Tests
{
    [TestClass]
    public class InternalProcessStateMachineValidationTests
    {
        public enum TestState
        {
            Initial,
            Processing,
            Completed,
            Failed,
            Orphaned
        }

        public class TestObject : IStateful<TestState>
        {
            public string Id { get; set; } = Guid.NewGuid().ToString();
            public TestState Status { get; set; } = TestState.Initial;
        }

        [TestMethod]
        public void ValidateConfiguration_WithValidConfiguration_ReturnsNoErrors()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            stateMachine
                .ConfigureState(TestState.Initial, TestTask)
                .ConfigureState(TestState.Processing, TestTask)
                .ConfigureState(TestState.Completed, TestTask)
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing)
                .AddTransition(TestState.Processing, InternalProcessState.Succeeded, TestState.Completed)
                .AddFinalTransition(TestState.Completed, InternalProcessState.Succeeded);

            // Act
            var errors = stateMachine.ValidateConfiguration();

            // Assert
            Assert.IsFalse(errors.Any(), $"Expected no validation errors, but got: {string.Join("; ", errors)}");
        }

        [TestMethod]
        public void ValidateConfiguration_WithMissingStateConfiguration_ReturnsError()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            // Add transition but no state configuration
            stateMachine.AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Act
            var errors = stateMachine.ValidateConfiguration().ToList();

            // Assert
            Assert.IsTrue(errors.Any());
            Assert.IsTrue(errors.Any(e => e.Contains("Processing") && e.Contains("no configuration")));
        }

        [TestMethod]
        public void ValidateConfiguration_WithUnreachableState_ReturnsWarning()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            stateMachine
                .ConfigureState(TestState.Initial, TestTask)
                .ConfigureState(TestState.Orphaned, TestTask) // This state has no incoming transitions
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Act
            var errors = stateMachine.ValidateConfiguration().ToList();

            // Assert
            Assert.IsTrue(errors.Any());
            Assert.IsTrue(errors.Any(e => e.Contains("Orphaned") && e.Contains("no incoming transitions")));
        }

        [TestMethod]
        public void ValidateConfiguration_WithDeadEndState_ReturnsWarning()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            stateMachine
                .ConfigureState(TestState.Initial, TestTask)
                .ConfigureState(TestState.Processing, TestTask); // This state has no outgoing transitions

            // Act
            var errors = stateMachine.ValidateConfiguration().ToList();

            // Assert
            Assert.IsTrue(errors.Any());
            Assert.IsTrue(errors.Any(e => e.Contains("Processing") && e.Contains("no outgoing transitions")));
        }

        [TestMethod]
        public void GetAvailableTransitions_ReturnsCorrectTransitions()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            stateMachine
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing)
                .AddTransition(TestState.Initial, InternalProcessState.Failed, TestState.Failed)
                .AddTransition(TestState.Processing, InternalProcessState.Succeeded, TestState.Completed);

            // Act
            var availableTransitions = stateMachine.GetAvailableTransitions().ToList();

            // Assert
            Assert.AreEqual(2, availableTransitions.Count);
            Assert.IsTrue(availableTransitions.Any(t => t.Trigger == InternalProcessState.Succeeded && t.ToState.Equals(TestState.Processing)));
            Assert.IsTrue(availableTransitions.Any(t => t.Trigger == InternalProcessState.Failed && t.ToState.Equals(TestState.Failed)));
        }

        [TestMethod]
        public void GetAllTransitions_ReturnsAllConfiguredTransitions()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            stateMachine
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing)
                .AddTransition(TestState.Processing, InternalProcessState.Succeeded, TestState.Completed)
                .AddFinalTransition(TestState.Completed, InternalProcessState.Succeeded);

            // Act
            var allTransitions = stateMachine.GetAllTransitions().ToList();

            // Assert
            Assert.AreEqual(3, allTransitions.Count);
            Assert.IsTrue(allTransitions.Any(t => t.IsFinalTransition));
        }

        [TestMethod]
        public void GetConfiguredStates_ReturnsAllConfiguredStates()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            stateMachine
                .ConfigureState(TestState.Initial, TestTask)
                .ConfigureState(TestState.Processing, TestTask)
                .ConfigureState(TestState.Completed, TestTask);

            // Act
            var configuredStates = stateMachine.GetConfiguredStates().ToList();

            // Assert
            Assert.AreEqual(3, configuredStates.Count);
            Assert.IsTrue(configuredStates.Contains(TestState.Initial));
            Assert.IsTrue(configuredStates.Contains(TestState.Processing));
            Assert.IsTrue(configuredStates.Contains(TestState.Completed));
        }

        [TestMethod]
        public async Task ExecuteToCompletion_WithMaxIterations_StopsAtLimit()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            // Configure a state that never finishes
            stateMachine.ConfigureState(TestState.Initial, InfiniteLoopTask);

            // Act
            var result = await stateMachine.ExecuteToCompletion(maxIterations: 5);

            // Assert
            Assert.IsFalse(result); // Should not complete within 5 iterations
            Assert.IsFalse(stateMachine.IsFinished);
        }

        [TestMethod]
        public async Task ExecuteToCompletion_WithValidWorkflow_CompletesSuccessfully()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            
            stateMachine
                .ConfigureState(TestState.Initial, TestTask)
                .ConfigureState(TestState.Processing, TestTask)
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing)
                .AddFinalTransition(TestState.Processing, InternalProcessState.Succeeded);

            // Act
            var result = await stateMachine.ExecuteToCompletion();

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(stateMachine.IsFinished);
        }

        [TestMethod]
        public async Task ExecuteIteration_WithTimeout_ReturnsFalse()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            stateMachine.ConfigureState(TestState.Initial, SlowTask, "Slow task", TimeSpan.FromMilliseconds(10));

            // Act
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsFalse(result);
            Assert.IsNotNull(stateMachine.LastException);
            Assert.IsInstanceOfType(stateMachine.LastException, typeof(StateTaskExecutionException));
            Assert.IsTrue(stateMachine.GetLastFailureReason().Contains("State task execution failed"));
        }

        [TestMethod]
        public async Task ExecuteIteration_WithTaskException_ReturnsFalse()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            stateMachine.ConfigureState(TestState.Initial, ThrowingTask);

            // Act
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsFalse(result);
            Assert.IsNotNull(stateMachine.LastException);
            Assert.IsInstanceOfType(stateMachine.LastException, typeof(StateTaskExecutionException));

            var failureReason = stateMachine.GetLastFailureReason();
            Assert.IsTrue(failureReason.Contains("State task execution failed"));
        }

        // Helper methods for testing
        private static async Task<InternalProcessState> TestTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            await Task.Delay(1);
            return InternalProcessState.Succeeded;
        }

        private static async Task<InternalProcessState> InfiniteLoopTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            await Task.Delay(1);
            return InternalProcessState.Succeeded; // But no transition configured, so it will fail
        }

        private static async Task<InternalProcessState> SlowTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            await Task.Delay(100); // This will timeout with 10ms limit
            return InternalProcessState.Succeeded;
        }

        private static async Task<InternalProcessState> ThrowingTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            await Task.Delay(1);
            throw new Exception("Test exception");
        }
    }
}
