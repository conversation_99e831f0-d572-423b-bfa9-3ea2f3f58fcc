using Microsoft.VisualStudio.TestTools.UnitTesting;
using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Tests
{
    [TestClass]
    public class InternalProcessStateMachineRetryTests
    {
        public enum TestState
        {
            Initial,
            Processing,
            Completed,
            Failed
        }

        public class TestObject : IStateful<TestState>
        {
            public string Id { get; set; } = Guid.NewGuid().ToString();
            public TestState Status { get; set; } = TestState.Initial;
            public int TaskExecutionCount { get; set; } = 0;
        }

        [TestMethod]
        public async Task ExecuteIteration_WithRetryState_IncrementsRetryCount()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine.ConfigureState(TestState.Initial, RetryTask);

            // Act
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual(InternalProcessState.Retry, stateMachine.InternalState);
            Assert.AreEqual(1, stateMachine.StateData.RetryCount);
            Assert.AreEqual(TestState.Initial, stateMachine.CurrentState); // Should not transition
        }

        [TestMethod]
        public async Task ExecuteIteration_WithMultipleRetries_IncrementsCountCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine.ConfigureState(TestState.Initial, RetryTask);

            // Act
            await stateMachine.ExecuteIteration(); // First retry
            await stateMachine.ExecuteIteration(); // Second retry
            await stateMachine.ExecuteIteration(); // Third retry

            // Assert
            Assert.AreEqual(3, stateMachine.StateData.RetryCount);
            Assert.AreEqual(InternalProcessState.Retry, stateMachine.InternalState);
        }

        [TestMethod]
        public async Task RunStateTask_WithRetryLimitExceeded_ThrowsException()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new InternalProcessStateData<TestState, TestObject>(testObject)
            {
                InternalState = InternalProcessState.Retry,
                RetryCount = 3,
                MaxRetries = 3
            };
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(stateData);
            stateMachine.ConfigureState(TestState.Initial, RetryTask);

            // Act & Assert
            await Assert.ThrowsExceptionAsync<MaxRetryExceededException>(
                async () => await stateMachine.RunStateTask());
        }

        [TestMethod]
        public async Task ExecuteIteration_AfterSuccessfulRetry_ResetsRetryCount()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine
                .ConfigureState(TestState.Initial, ConditionalRetryTask)
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Act - First execution should retry
            await stateMachine.ExecuteIteration();
            Assert.AreEqual(1, stateMachine.StateData.RetryCount);

            // Second execution should succeed and transition
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual(0, stateMachine.StateData.RetryCount);
            Assert.AreEqual(TestState.Processing, stateMachine.CurrentState);
        }

        [TestMethod]
        public void StateData_IsRetryLimitExceeded_WorksCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new InternalProcessStateData<TestState, TestObject>(testObject)
            {
                MaxRetries = 3
            };

            // Act & Assert
            Assert.IsFalse(stateData.IsRetryLimitExceeded()); // 0 retries

            stateData.RetryCount = 2;
            Assert.IsFalse(stateData.IsRetryLimitExceeded()); // 2 retries

            stateData.RetryCount = 3;
            Assert.IsTrue(stateData.IsRetryLimitExceeded()); // 3 retries (limit reached)

            stateData.RetryCount = 4;
            Assert.IsTrue(stateData.IsRetryLimitExceeded()); // 4 retries (exceeded)
        }

        [TestMethod]
        public void StateData_UpdateInternalState_HandlesRetryCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new InternalProcessStateData<TestState, TestObject>(testObject);

            // Act - Set to retry
            stateData.UpdateInternalState(InternalProcessState.Retry);

            // Assert
            Assert.AreEqual(InternalProcessState.Retry, stateData.InternalState);
            Assert.AreEqual(1, stateData.RetryCount);

            // Act - Set to retry again
            stateData.UpdateInternalState(InternalProcessState.Retry);

            // Assert
            Assert.AreEqual(2, stateData.RetryCount);

            // Act - Set to succeeded (should reset retry count)
            stateData.UpdateInternalState(InternalProcessState.Succeeded);

            // Assert
            Assert.AreEqual(InternalProcessState.Succeeded, stateData.InternalState);
            Assert.AreEqual(0, stateData.RetryCount);
        }

        [TestMethod]
        public void StateData_ResetRetryCount_WorksCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new InternalProcessStateData<TestState, TestObject>(testObject)
            {
                RetryCount = 5
            };

            // Act
            stateData.ResetRetryCount();

            // Assert
            Assert.AreEqual(0, stateData.RetryCount);
        }

        // Helper methods for testing
        private static async Task<InternalProcessState> RetryTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            testObject.TaskExecutionCount++;
            await Task.Delay(1);
            return InternalProcessState.Retry;
        }

        private static async Task<InternalProcessState> ConditionalRetryTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            testObject.TaskExecutionCount++;
            await Task.Delay(1);
            
            // Retry on first execution, succeed on second
            return testObject.TaskExecutionCount == 1 ? InternalProcessState.Retry : InternalProcessState.Succeeded;
        }
    }
}
