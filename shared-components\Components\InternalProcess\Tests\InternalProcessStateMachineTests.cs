using Microsoft.VisualStudio.TestTools.UnitTesting;
using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Tests
{
    [TestClass]
    public class InternalProcessStateMachineTests
    {
        // Test enum for states
        public enum TestState
        {
            Initial,
            Processing,
            Completed,
            Failed
        }

        // Test class that implements IStateful
        public class TestObject : IStateful<TestState>
        {
            public string Id { get; set; } = Guid.NewGuid().ToString();
            public TestState Status { get; set; } = TestState.Initial;
            public List<string> ProcessingLog { get; set; } = new List<string>();
        }

        [TestMethod]
        public void Constructor_WithStatefulObject_SetsInitialState()
        {
            // Arrange
            var testObject = new TestObject();

            // Act
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Assert
            Assert.AreEqual(testObject, stateMachine.StateData.StatefulObject);
            Assert.AreEqual(TestState.Initial, stateMachine.CurrentState);
            Assert.AreEqual(InternalProcessState.Succeeded, stateMachine.InternalState);
            Assert.IsFalse(stateMachine.IsFinished);
        }

        [TestMethod]
        public void Constructor_WithStateData_LoadsCorrectly()
        {
            // Arrange
            var testObject = new TestObject { Status = TestState.Processing };
            var stateData = new InternalProcessStateData<TestState, TestObject>(testObject)
            {
                InternalState = InternalProcessState.Failed,
                RetryCount = 2
            };

            // Act
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(stateData);

            // Assert
            Assert.AreEqual(TestState.Processing, stateMachine.CurrentState);
            Assert.AreEqual(InternalProcessState.Failed, stateMachine.InternalState);
            Assert.AreEqual(2, stateMachine.StateData.RetryCount);
        }

        [TestMethod]
        public void ConfigureState_AddsStateConfiguration()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Act
            stateMachine.ConfigureState(TestState.Initial, TestTask, "Test description");

            // Assert
            var configuredStates = stateMachine.GetConfiguredStates();
            Assert.IsTrue(configuredStates.Contains(TestState.Initial));
        }

        [TestMethod]
        public void AddTransition_AddsTransitionCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Act
            stateMachine.AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Assert
            var transitions = stateMachine.GetAllTransitions();
            Assert.AreEqual(1, transitions.Count());
            
            var transition = transitions.First();
            Assert.AreEqual(TestState.Initial, transition.FromState);
            Assert.AreEqual(InternalProcessState.Succeeded, transition.Trigger);
            Assert.AreEqual(TestState.Processing, transition.ToState);
            Assert.IsFalse(transition.IsFinalTransition);
        }

        [TestMethod]
        public void AddFinalTransition_AddsFinalTransitionCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Act
            stateMachine.AddFinalTransition(TestState.Completed, InternalProcessState.Succeeded);

            // Assert
            var transitions = stateMachine.GetAllTransitions();
            var transition = transitions.First();
            Assert.IsTrue(transition.IsFinalTransition);
            Assert.AreEqual(TestState.Completed, transition.FromState);
            Assert.AreEqual(TestState.Completed, transition.ToState);
        }

        [TestMethod]
        public void SetCurrentState_UpdatesStateCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Act
            stateMachine.SetCurrentState(TestState.Processing, InternalProcessState.Failed);

            // Assert
            Assert.AreEqual(TestState.Processing, stateMachine.CurrentState);
            Assert.AreEqual(TestState.Processing, testObject.Status);
            Assert.AreEqual(InternalProcessState.Failed, stateMachine.InternalState);
        }

        [TestMethod]
        public async Task ExecuteIteration_ExecutesConfiguredTask()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine
                .ConfigureState(TestState.Initial, TestTask)
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Act
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(testObject.ProcessingLog.Contains("TestTask executed"));
            Assert.AreEqual(TestState.Processing, stateMachine.CurrentState);
        }

        [TestMethod]
        public async Task ExecuteIteration_WithUnconfiguredState_ReturnsFalse()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Act
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsFalse(result);
            Assert.IsNotNull(stateMachine.LastException);
            Assert.IsInstanceOfType(stateMachine.LastException, typeof(StateConfigurationException));
        }

        [TestMethod]
        public void TryTransition_WithValidTransition_ReturnsTrue()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine.AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Act
            var result = stateMachine.TryTransition(InternalProcessState.Succeeded);

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual(TestState.Processing, stateMachine.CurrentState);
        }

        [TestMethod]
        public void TryTransition_WithInvalidTransition_ReturnsFalse()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Act
            var result = stateMachine.TryTransition(InternalProcessState.Succeeded);

            // Assert
            Assert.IsFalse(result);
            Assert.AreEqual(TestState.Initial, stateMachine.CurrentState);
        }

        [TestMethod]
        public void TryTransition_WithFinalTransition_SetsFinishedState()
        {
            // Arrange
            var testObject = new TestObject { Status = TestState.Completed };
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine.AddFinalTransition(TestState.Completed, InternalProcessState.Succeeded);

            // Act
            var result = stateMachine.TryTransition(InternalProcessState.Succeeded);

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(stateMachine.IsFinished);
            Assert.AreEqual(InternalProcessState.Finished, stateMachine.InternalState);
        }

        [TestMethod]
        public async Task ExecuteIteration_WithSuccessfulTask_TransitionsCorrectly()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine
                .ConfigureState(TestState.Initial, TestTask)
                .AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Act
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsTrue(result);
            Assert.AreEqual(TestState.Processing, stateMachine.CurrentState);
            Assert.IsFalse(stateMachine.LastTransitionFailed);
        }

        [TestMethod]
        public async Task ExecuteIteration_WithFailedTransition_ReturnsFalse()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);
            stateMachine.ConfigureState(TestState.Initial, TestTask);
            // No transition configured for Succeeded state

            // Act
            var result = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsFalse(result);
            Assert.IsTrue(stateMachine.LastTransitionFailed);
            Assert.IsFalse(stateMachine.LastFailureWasRetryLimitExceeded);
            Assert.IsNotNull(stateMachine.LastException);

            var failureReason = stateMachine.GetLastFailureReason();
            Assert.IsTrue(failureReason.Contains("Transition failed"));
        }

        [TestMethod]
        public void IsRetryLimitExceeded_ReturnsCorrectValue()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new InternalProcessStateData<TestState, TestObject>(testObject)
            {
                RetryCount = 3,
                MaxRetries = 3
            };
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(stateData);

            // Act & Assert
            Assert.IsTrue(stateMachine.IsRetryLimitExceeded());

            stateData.RetryCount = 2;
            Assert.IsFalse(stateMachine.IsRetryLimitExceeded());
        }

        [TestMethod]
        public void GetLastFailureReason_WithNoFailure_ReturnsNoFailure()
        {
            // Arrange
            var testObject = new TestObject();
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>(testObject);

            // Act
            var reason = stateMachine.GetLastFailureReason();

            // Assert
            Assert.AreEqual("No failure recorded", reason);
        }

        // Helper method for testing
        private static async Task<InternalProcessState> TestTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            testObject.ProcessingLog.Add("TestTask executed");
            await Task.Delay(1); // Simulate async work
            return InternalProcessState.Succeeded;
        }

        private static async Task<InternalProcessState> FailingTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            testObject.ProcessingLog.Add("FailingTask executed");
            await Task.Delay(1);
            return InternalProcessState.Failed;
        }

        private static async Task<InternalProcessState> RetryTask(TestObject testObject, InternalProcessState currentInternalState)
        {
            testObject.ProcessingLog.Add("RetryTask executed");
            await Task.Delay(1);
            return InternalProcessState.Retry;
        }
    }
}
