# InternalProcess State Machine

A generic, reusable state machine implementation designed for managing internal processes in controllers. This component provides a robust framework for handling complex workflows with state transitions, retry logic, and error handling.

## Features

- **Generic State Machine**: Works with any state type and IStateful payload objects
- **Retry Logic**: Built-in retry mechanism with configurable limits
- **State Persistence**: Serializable state data for easy save/load operations
- **Timeout Handling**: Configurable timeouts for state tasks
- **Validation**: Configuration validation to catch setup errors
- **Final Transitions**: Special transitions that mark processes as finished
- **Thread-Safe**: Safe for concurrent access
- **Comprehensive Testing**: Full test coverage with examples

## Core Components

### InternalProcessState Enum

Defines the internal states that control state machine flow:

- `Finished`: Process completed successfully (terminal state)
- `Failed`: Current task failed, next state should handle failure
- `Succeeded`: Current task succeeded, continue normal workflow
- `Retry`: Retry current state (increments retry counter)

### InternalProcessStateData<TState, TObject>

Serializable class containing complete state machine data:

- Internal process state and retry information
- IStateful payload object
- Correlation ID for tracking
- Timestamps and retry limits

### IInternalProcessStateMachine<TState, TObject>

Main interface providing:

- State configuration and transition management
- Task execution and iteration control
- State validation and introspection
- Persistence support
- Comprehensive error reporting and failure analysis

## Quick Start

### 1. Define Your State Enum and Payload Class

```csharp
public enum DocumentProcessingState
{
    Queued,
    Validating,
    Processing,
    Completed,
    Failed
}

public class ProcessingDocument : IStateful<DocumentProcessingState>
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Content { get; set; } = string.Empty;
    public DocumentProcessingState Status { get; set; } = DocumentProcessingState.Queued;
    public List<string> ValidationErrors { get; set; } = new List<string>();
}
```

### 2. Create and Configure the State Machine

```csharp
var document = new ProcessingDocument { Content = "Sample content" };
var stateMachine = new InternalProcessStateMachine<DocumentProcessingState, ProcessingDocument>(document);

// Configure states with their tasks
stateMachine
    .ConfigureState(DocumentProcessingState.Queued, ValidateDocumentTask, "Validate document")
    .ConfigureState(DocumentProcessingState.Validating, ProcessDocumentTask, "Process document")
    .ConfigureState(DocumentProcessingState.Processing, CompleteDocumentTask, "Complete processing");

// Configure transitions
stateMachine
    .AddTransition(DocumentProcessingState.Queued, InternalProcessState.Succeeded, DocumentProcessingState.Validating)
    .AddTransition(DocumentProcessingState.Queued, InternalProcessState.Failed, DocumentProcessingState.Failed)
    .AddTransition(DocumentProcessingState.Validating, InternalProcessState.Succeeded, DocumentProcessingState.Processing)
    .AddTransition(DocumentProcessingState.Validating, InternalProcessState.Failed, DocumentProcessingState.Failed)
    .AddTransition(DocumentProcessingState.Processing, InternalProcessState.Succeeded, DocumentProcessingState.Completed)
    .AddTransition(DocumentProcessingState.Processing, InternalProcessState.Failed, DocumentProcessingState.Failed);

// Add final transitions
stateMachine
    .AddFinalTransition(DocumentProcessingState.Completed, InternalProcessState.Succeeded)
    .AddFinalTransition(DocumentProcessingState.Failed, InternalProcessState.Failed);
```

### 3. Execute the State Machine

```csharp
// Execute to completion
var success = await stateMachine.ExecuteToCompletion();

// Or execute step by step with error handling
while (!stateMachine.IsFinished)
{
    var iterationSuccess = await stateMachine.ExecuteIteration();
    if (!iterationSuccess)
    {
        // Check what went wrong
        if (stateMachine.LastFailureWasRetryLimitExceeded)
        {
            Console.WriteLine("Retry limit exceeded");
        }
        else if (stateMachine.LastTransitionFailed)
        {
            Console.WriteLine("Transition failed");
        }

        // Get detailed failure reason
        Console.WriteLine($"Failure reason: {stateMachine.GetLastFailureReason()}");
        break;
    }

    // Save state for persistence if needed
    var stateData = stateMachine.StateData;
}
```

### 4. Implement State Tasks

```csharp
private static async Task<InternalProcessState> ValidateDocumentTask(
    ProcessingDocument document, 
    InternalProcessState currentInternalState)
{
    // Perform validation logic
    if (string.IsNullOrWhiteSpace(document.Content))
    {
        document.ValidationErrors.Add("Content is required");
        return InternalProcessState.Failed;
    }
    
    // Simulate async work
    await Task.Delay(100);
    
    return InternalProcessState.Succeeded;
}
```

## Dependency Injection

Register the services in your `Startup.cs` or `Program.cs`:

```csharp
public void ConfigureServices(IServiceCollection services)
{
    // Register all InternalProcess services
    services.AddInternalProcess();
    
    // Or register individual components
    services.AddInternalProcessStateMachine();
    services.AddInternalProcessStateMachineFactory();
}
```

Use in controllers:

```csharp
public class MyController : ControllerBase
{
    private readonly IInternalProcessStateMachineFactory _stateMachineFactory;
    
    public MyController(IInternalProcessStateMachineFactory stateMachineFactory)
    {
        _stateMachineFactory = stateMachineFactory;
    }
    
    [HttpPost("process")]
    public async Task<IActionResult> ProcessData(MessageBusMessage message)
    {
        var payload = message.GetPayload<MyPayload>();
        var stateMachine = _stateMachineFactory.Create(payload);
        
        // Configure and execute state machine
        // ...
    }
}
```

## Error Handling and Diagnostics

The state machine provides comprehensive error reporting to help diagnose issues:

```csharp
var success = await stateMachine.ExecuteIteration();
if (!success)
{
    // Check specific failure types
    if (stateMachine.LastFailureWasRetryLimitExceeded)
    {
        Console.WriteLine("Process failed due to retry limit");
        Console.WriteLine($"Retry count: {stateMachine.StateData.RetryCount}");
    }

    if (stateMachine.LastTransitionFailed)
    {
        Console.WriteLine("No valid transition found");
    }

    // Get detailed failure description
    var reason = stateMachine.GetLastFailureReason();
    Console.WriteLine($"Detailed reason: {reason}");

    // Access the underlying exception if needed
    var exception = stateMachine.LastException;
    if (exception != null)
    {
        Console.WriteLine($"Exception: {exception.GetType().Name}: {exception.Message}");
    }
}
```

## Advanced Features

### Retry Logic

Tasks can return `InternalProcessState.Retry` to retry the current state:

```csharp
private static async Task<InternalProcessState> ExternalApiTask(
    MyObject obj, 
    InternalProcessState currentInternalState)
{
    try
    {
        await CallExternalApi();
        return InternalProcessState.Succeeded;
    }
    catch (HttpRequestException)
    {
        // Retry on network errors
        return InternalProcessState.Retry;
    }
    catch (Exception)
    {
        // Fail on other errors
        return InternalProcessState.Failed;
    }
}
```

### State Persistence

State data is fully serializable for persistence:

```csharp
// Save state
var stateDataJson = JsonSerializer.Serialize(stateMachine.StateData);
await SaveToDatabase(stateDataJson);

// Load state
var stateData = JsonSerializer.Deserialize<InternalProcessStateData<MyState, MyObject>>(stateDataJson);
var restoredStateMachine = new InternalProcessStateMachine<MyState, MyObject>(stateData);
```

### Configuration Validation

Validate your state machine configuration:

```csharp
var errors = stateMachine.ValidateConfiguration();
if (errors.Any())
{
    throw new StateMachineConfigurationException(errors);
}
```

## Examples

See the `Examples` folder for comprehensive usage examples:

- `BasicUsageExample.cs`: Simple document processing workflow
- `AdvancedUsageExample.cs`: Order processing with retry logic and persistence
- `ControllerIntegrationExample.cs`: Integration with ASP.NET Core controllers

## Testing

The component includes comprehensive unit tests covering:

- Basic state machine operations
- Retry logic and limits
- Configuration validation
- Error handling and edge cases
- Timeout scenarios

Run tests with:
```bash
dotnet test shared-components/Components/InternalProcess/Tests/
```

## Migration from Stateless

This component can replace the existing `Stateless` library usage in `SuperController`. The new implementation provides:

- Better integration with the existing codebase patterns
- Built-in retry logic without external configuration
- Serializable state for persistence
- More comprehensive error handling
- Consistent with other shared components

## Best Practices

1. **Keep state tasks focused**: Each task should handle one specific operation
2. **Use descriptive state names**: Make the workflow easy to understand
3. **Handle errors gracefully**: Return appropriate internal states for different error conditions
4. **Validate configuration**: Always validate before executing in production
5. **Use correlation IDs**: Track state machine executions for debugging
6. **Set appropriate timeouts**: Prevent hanging on external calls
7. **Test thoroughly**: Use the provided test patterns for your implementations

## License

This component is part of the shared-components library and follows the same licensing terms.
