using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Interfaces;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;
using System.Text.Json.Serialization;

namespace shared.Components.InternalProcess.Examples
{
    /// <summary>
    /// Example demonstrating basic usage of the InternalProcess state machine.
    /// This example shows a simple document processing workflow.
    /// </summary>
    public class BasicUsageExample
    {
        // Example enum for document processing states
        public enum DocumentProcessingState
        {
            Queued,
            Validating,
            Processing,
            Completed,
            Failed
        }

        // Example document class that implements IStateful
        public class ProcessingDocument : IStateful<DocumentProcessingState>
        {
            public string Id { get; set; } = Guid.NewGuid().ToString();
            public string Content { get; set; } = string.Empty;
            public DocumentProcessingState Status { get; set; } = DocumentProcessingState.Queued;
            public List<string> ValidationErrors { get; set; } = new List<string>();
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        }

        /// <summary>
        /// Demonstrates how to create and configure a basic state machine.
        /// </summary>
        public static async Task<bool> RunBasicExample()
        {
            // Create a document to process
            var document = new ProcessingDocument
            {
                Content = "Sample document content for processing"
            };

            // Create the state machine
            var stateMachine = new InternalProcessStateMachine<DocumentProcessingState, ProcessingDocument>(document);

            // Configure states with their tasks
            stateMachine
                .ConfigureState(DocumentProcessingState.Queued, ValidateDocumentTask, "Validate document format and content")
                .ConfigureState(DocumentProcessingState.Validating, ProcessDocumentTask, "Process the document content")
                .ConfigureState(DocumentProcessingState.Processing, CompleteDocumentTask, "Finalize document processing");

            // Configure transitions
            stateMachine
                .AddTransition(DocumentProcessingState.Queued, InternalProcessState.Succeeded, DocumentProcessingState.Validating)
                .AddTransition(DocumentProcessingState.Queued, InternalProcessState.Failed, DocumentProcessingState.Failed)
                .AddTransition(DocumentProcessingState.Validating, InternalProcessState.Succeeded, DocumentProcessingState.Processing)
                .AddTransition(DocumentProcessingState.Validating, InternalProcessState.Failed, DocumentProcessingState.Failed)
                .AddTransition(DocumentProcessingState.Processing, InternalProcessState.Succeeded, DocumentProcessingState.Completed)
                .AddTransition(DocumentProcessingState.Processing, InternalProcessState.Failed, DocumentProcessingState.Failed);

            // Add final transitions to mark the process as finished
            stateMachine
                .AddFinalTransition(DocumentProcessingState.Completed, InternalProcessState.Succeeded, "Document processing completed successfully")
                .AddFinalTransition(DocumentProcessingState.Failed, InternalProcessState.Failed, "Document processing failed");

            // Validate configuration
            var validationErrors = stateMachine.ValidateConfiguration();
            if (validationErrors.Any())
            {
                Console.WriteLine("Configuration errors:");
                foreach (var error in validationErrors)
                {
                    Console.WriteLine($"  - {error}");
                }
                return false;
            }

            // Execute the state machine to completion
            Console.WriteLine($"Starting document processing for document {document.Id}");
            Console.WriteLine($"Initial state: {document.Status}");

            var success = await stateMachine.ExecuteToCompletion();

            Console.WriteLine($"Processing completed: {success}");
            Console.WriteLine($"Final state: {document.Status}");
            Console.WriteLine($"Is finished: {stateMachine.IsFinished}");

            if (!success)
            {
                Console.WriteLine($"Last transition failed: {stateMachine.LastTransitionFailed}");
                Console.WriteLine($"Last internal state: {stateMachine.LastInternalState}");
            }

            return success;
        }

        /// <summary>
        /// Task for validating the document.
        /// </summary>
        private static async Task<InternalProcessState> ValidateDocumentTask(ProcessingDocument document, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Validating document {document.Id}...");
            
            // Simulate validation work
            await Task.Delay(100);

            // Simple validation logic
            if (string.IsNullOrWhiteSpace(document.Content))
            {
                document.ValidationErrors.Add("Document content is empty");
                return InternalProcessState.Failed;
            }

            if (document.Content.Length < 10)
            {
                document.ValidationErrors.Add("Document content is too short");
                return InternalProcessState.Failed;
            }

            Console.WriteLine("Document validation passed");
            return InternalProcessState.Succeeded;
        }

        /// <summary>
        /// Task for processing the document.
        /// </summary>
        private static async Task<InternalProcessState> ProcessDocumentTask(ProcessingDocument document, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Processing document {document.Id}...");
            
            // Simulate processing work
            await Task.Delay(200);

            // Simple processing logic - could fail randomly for demonstration
            var random = new Random();
            if (random.NextDouble() < 0.1) // 10% chance of failure
            {
                document.ValidationErrors.Add("Processing failed due to system error");
                return InternalProcessState.Failed;
            }

            // Transform the content (example processing)
            document.Content = document.Content.ToUpperCase();

            Console.WriteLine("Document processing completed");
            return InternalProcessState.Succeeded;
        }

        /// <summary>
        /// Task for completing the document processing.
        /// </summary>
        private static async Task<InternalProcessState> CompleteDocumentTask(ProcessingDocument document, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Finalizing document {document.Id}...");
            
            // Simulate finalization work
            await Task.Delay(50);

            // Add completion timestamp or other finalization logic
            Console.WriteLine("Document processing finalized");
            return InternalProcessState.Succeeded;
        }
    }
}
