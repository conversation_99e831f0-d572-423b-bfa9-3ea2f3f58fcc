using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Interfaces
{
    /// <summary>
    /// Interface for an internal process state machine that manages state transitions and task execution.
    /// Provides a generic, reusable state machine implementation for internal processes.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public interface IInternalProcessStateMachine<TState, TObject> 
        where TObject : IStateful<TState>
        where TState : notnull
    {
        /// <summary>
        /// Gets the current state data of the state machine.
        /// </summary>
        InternalProcessStateData<TState, TObject> StateData { get; }

        /// <summary>
        /// Gets the current state of the IStateful object.
        /// </summary>
        TState CurrentState { get; }

        /// <summary>
        /// Gets the current internal process state.
        /// </summary>
        InternalProcessState InternalState { get; }

        /// <summary>
        /// Gets whether the state machine has finished processing.
        /// </summary>
        bool IsFinished { get; }

        /// <summary>
        /// Gets the last internal state before the current one (for error tracking).
        /// </summary>
        InternalProcessState? LastInternalState { get; }

        /// <summary>
        /// Gets whether the last transition failed.
        /// </summary>
        bool LastTransitionFailed { get; }

        /// <summary>
        /// Configures a state with its associated task.
        /// </summary>
        /// <param name="state">The state to configure</param>
        /// <param name="stateTask">The task to execute when in this state</param>
        /// <param name="description">Optional description of the state</param>
        /// <param name="taskTimeout">Maximum execution time for the task</param>
        /// <returns>The state machine instance for method chaining</returns>
        IInternalProcessStateMachine<TState, TObject> ConfigureState(
            TState state,
            Func<TObject, InternalProcessState, Task<InternalProcessState>> stateTask,
            string? description = null,
            TimeSpan? taskTimeout = null);

        /// <summary>
        /// Adds a transition between states.
        /// </summary>
        /// <param name="fromState">Source state</param>
        /// <param name="trigger">Internal process state that triggers the transition</param>
        /// <param name="toState">Destination state</param>
        /// <param name="description">Optional description of the transition</param>
        /// <returns>The state machine instance for method chaining</returns>
        IInternalProcessStateMachine<TState, TObject> AddTransition(
            TState fromState,
            InternalProcessState trigger,
            TState toState,
            string? description = null);

        /// <summary>
        /// Adds a final transition that marks the process as finished.
        /// Final transitions don't change the IStateful object's state but set internal state to Finished.
        /// </summary>
        /// <param name="fromState">Source state</param>
        /// <param name="trigger">Internal process state that triggers the final transition</param>
        /// <param name="description">Optional description of the transition</param>
        /// <returns>The state machine instance for method chaining</returns>
        IInternalProcessStateMachine<TState, TObject> AddFinalTransition(
            TState fromState,
            InternalProcessState trigger,
            string? description = null);

        /// <summary>
        /// Sets the current state of the state machine and the IStateful object.
        /// This allows loading state from external configuration or resuming from a saved state.
        /// </summary>
        /// <param name="state">The state to set</param>
        /// <param name="internalState">The internal process state to set</param>
        void SetCurrentState(TState state, InternalProcessState internalState = InternalProcessState.Succeeded);

        /// <summary>
        /// Loads the complete state data into the state machine.
        /// This allows resuming from a previously saved state.
        /// </summary>
        /// <param name="stateData">The state data to load</param>
        void LoadStateData(InternalProcessStateData<TState, TObject> stateData);

        /// <summary>
        /// Executes the task for the current state and returns the resulting internal state.
        /// This method runs the StateTask associated with the current state.
        /// </summary>
        /// <returns>The internal process state returned by the state task</returns>
        Task<InternalProcessState> RunStateTask();

        /// <summary>
        /// Executes one complete iteration of the state machine.
        /// This runs the current state task and then attempts to transition based on the result.
        /// </summary>
        /// <returns>True if the iteration completed successfully, false if it failed</returns>
        Task<bool> ExecuteIteration();

        /// <summary>
        /// Executes the state machine until it reaches a finished state or fails.
        /// This method will continue running iterations until the process is complete.
        /// </summary>
        /// <param name="maxIterations">Maximum number of iterations to prevent infinite loops</param>
        /// <returns>True if the process completed successfully, false if it failed or exceeded max iterations</returns>
        Task<bool> ExecuteToCompletion(int maxIterations = 100);

        /// <summary>
        /// Attempts to transition to a new state based on the given internal process state.
        /// </summary>
        /// <param name="internalState">The internal state that should trigger a transition</param>
        /// <returns>True if a transition occurred, false if no valid transition was found</returns>
        bool TryTransition(InternalProcessState internalState);

        /// <summary>
        /// Gets all configured transitions from the current state.
        /// </summary>
        /// <returns>List of transitions available from the current state</returns>
        IEnumerable<StateTransition<TState>> GetAvailableTransitions();

        /// <summary>
        /// Gets all configured transitions in the state machine.
        /// </summary>
        /// <returns>List of all transitions</returns>
        IEnumerable<StateTransition<TState>> GetAllTransitions();

        /// <summary>
        /// Gets all configured states in the state machine.
        /// </summary>
        /// <returns>List of all configured states</returns>
        IEnumerable<TState> GetConfiguredStates();

        /// <summary>
        /// Validates the state machine configuration.
        /// Checks for missing state configurations, unreachable states, etc.
        /// </summary>
        /// <returns>List of validation errors, empty if configuration is valid</returns>
        IEnumerable<string> ValidateConfiguration();
    }
}
